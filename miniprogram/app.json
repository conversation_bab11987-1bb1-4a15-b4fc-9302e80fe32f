{"pages": ["pages/index/index", "pages/chat/chat", "pages/profile/profile", "pages/preview/preview", "pages/about/about", "pages/profile-test/profile-test", "pages/chatBot/chatBot", "pages/ai-test/ai-test", "pages/foodBuy/foodBuy", "pages/test/test"], "usingComponents": {"agent-ui": "/components/agent-ui/index", "progress-ring": "/components/resume/progress-ring/index", "resume-card": "/components/resume/resume-card/index", "skill-tag": "/components/resume/skill-tag/index", "template-selector": "/components/resume/template-selector/index", "custom-map": "/components/toolCard/map/index", "custom-weather": "/components/toolCard/weather/index", "custom-food-list": "/components/toolCard/food-list/index", "custom-business-list": "/components/toolCard/business-list/index", "ui-button": "/components/ui/button/index"}, "window": {"navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black", "navigationBarTitleText": "简历故事", "backgroundColor": "#ffffff", "backgroundTextStyle": "dark", "enablePullDownRefresh": false}, "sitemapLocation": "sitemap.json", "style": "v2", "lazyCodeLoading": "requiredComponents", "networkTimeout": {"request": 300000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}}