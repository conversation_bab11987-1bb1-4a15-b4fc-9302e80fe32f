/* pages/index/index.wxss */

/**
 * 首页设计 - ChatGPT风格的直接功能入口
 * 简洁、直观、以功能为核心
 */

/* ========================================
   页面布局
   ======================================== */

.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
}

/* ========================================
   头部区域
   ======================================== */

.header-section {
  padding: 60rpx 40rpx 40rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.brand-minimal {
  text-align: center;
  margin-bottom: 40rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}

.app-subtitle {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.user-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.progress-text {
  font-size: 24rpx;
  color: #7f8c8d;
}

/* ========================================
   主要功能区域
   ======================================== */

.main-section {
  flex: 1;
  padding: 40rpx;
}

/* 快速开始区域 */
.quick-start-section {
  margin-bottom: 60rpx;
}

.section-title {
  text-align: center;
  margin-bottom: 40rpx;
}

.title-text {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12rpx;
}

.subtitle-text {
  font-size: 26rpx;
  color: #7f8c8d;
}

.quick-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.quick-option {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.quick-option:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
}

.option-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  margin-right: 24rpx;
}

.icon-text {
  font-size: 36rpx;
}

.option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.option-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.option-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

.option-arrow,
.recent-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-text {
  font-size: 24rpx;
  color: #bdc3c7;
}

/* 已登录用户功能区域 */
.main-functions {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.new-chat-section {
  text-align: center;
  padding: 60rpx 40rpx;
  background: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.new-chat-btn {
  margin-bottom: 20rpx;
}

.new-chat-desc {
  font-size: 26rpx;
  color: #7f8c8d;
}

/* 最近对话区域 */
.recent-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.recent-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: background-color 0.2s ease;
}

.recent-item:active {
  background: #e9ecef;
}

.recent-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.recent-title {
  font-size: 26rpx;
  color: #2c3e50;
  font-weight: 500;
}

.recent-time {
  font-size: 22rpx;
  color: #95a5a6;
}

/* 快捷功能网格 */
.quick-functions {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.function-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}

.function-item:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.function-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
}

.function-text {
  font-size: 24rpx;
  color: #2c3e50;
  font-weight: 500;
}

/* 授权提示区域 */
.auth-prompt {
  padding: 40rpx;
  background: white;
  text-align: center;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.prompt-text {
  display: block;
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 30rpx;
  line-height: 1.5;
}

/* 响应式设计 */
@media (min-width: 768rpx) {
  .function-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .quick-options {
    max-width: 600rpx;
    margin: 0 auto;
  }
}


/* ========================================
   响应式设计
   ======================================== */

@media (max-width: 600rpx) {
  .hero-title {
    font-size: 40rpx;
  }

  .brand-name {
    font-size: 60rpx;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .action-buttons .btn {
    width: 100%;
  }

  .footer-info {
    gap: 32rpx;
  }
}