/* pages/about/about.wxss */
.about-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 头部区域 */
.header-section {
  padding: 80rpx 40rpx 60rpx;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
}

.brand-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}

.brand-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.brand-name {
  font-size: 56rpx;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: 2rpx;
}

.brand-tagline {
  font-size: 28rpx;
  color: #666;
  font-weight: 300;
  letter-spacing: 4rpx;
}

.brand-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  max-width: 600rpx;
}

.hero-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
}

.hero-subtitle {
  font-size: 28rpx;
  color: #7f8c8d;
  line-height: 1.5;
  text-align: center;
}

/* 通用区域样式 */
.value-section,
.features-section,
.stats-section {
  padding: 60rpx 40rpx;
  background: white;
  margin: 20rpx 0;
}

.section-title {
  text-align: center;
  margin-bottom: 50rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
}

/* 价值主张区域 */
.value-list {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.value-item {
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border-left: 6rpx solid #3498db;
}

.value-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.value-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #2c3e50;
}

.value-description {
  font-size: 26rpx;
  color: #7f8c8d;
  line-height: 1.5;
}

/* 产品特色区域 */
.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  flex-shrink: 0;
}

.icon-text {
  font-size: 36rpx;
}

.feature-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  flex: 1;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.feature-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  line-height: 1.4;
}

/* 统计数据区域 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 操作区域 */
.action-section {
  padding: 60rpx 40rpx;
  background: white;
  text-align: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  max-width: 400rpx;
  margin: 0 auto;
}

/* 底部区域 */
.footer-section {
  padding: 60rpx 40rpx 40rpx;
  background: #2c3e50;
  color: white;
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 40rpx;
  margin-bottom: 30rpx;
}

.footer-link {
  font-size: 26rpx;
  color: #bdc3c7;
  padding: 10rpx;
}

.footer-info {
  padding-top: 30rpx;
  border-top: 1rpx solid #34495e;
}

.footer-text {
  font-size: 24rpx;
  color: #95a5a6;
}

/* 响应式设计 */
@media (min-width: 768rpx) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .action-buttons {
    flex-direction: row;
    justify-content: center;
  }

  .footer-links {
    gap: 60rpx;
  }
}